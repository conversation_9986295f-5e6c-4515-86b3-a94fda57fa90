import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { PrismaService } from '../shared/module/prisma/prisma.service';
import { EmailService } from '../shared/services/email.service';
import * as admin from 'firebase-admin';
import { AUTH_TYPE, STATUS } from '@prisma/client';
import { AuthDto, VerifyEmailDto, SignupDto, LoginDto, SendVerificationEmailDto } from './dto/auth.dto';
import { authMessages, userMessage } from '../shared/keys/user.key';
import { generateRandomId } from '../shared/module/bcrypt/bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
  ) {}

  async authenticate(
    firebaseUid: string,
    authType: AUTH_TYPE,
    fcmToken?: string,
    userData?: AuthDto,
  ) {
    try {
      // Get Firebase user data
      const firebaseUser = await admin.auth().getUser(firebaseUid);

      // Check if user exists
      let user = await this.prisma.user.findUnique({
        where: { firebaseUid },
      });

      if (!user) {
        // Create new user
        user = await this.prisma.user.create({
          data: {
            firebaseUid,
            email: firebaseUser.email || '',
            emailVerified: firebaseUser.emailVerified || false,
            firstName:
              userData?.firstName ||
              firebaseUser.displayName?.split(' ')[0] ||
              '',
            lastName:
              userData?.lastName ||
              firebaseUser.displayName?.split(' ')[1] ||
              '',
            authType,
            profileImage: firebaseUser.photoURL || null,
          },
        });
      }

      // Create or update user session
      let userSession = await this.prisma.userSession.findFirst({
        where: { userId: user.id, status: STATUS.ENABLED },
      });

      if (!userSession) {
        userSession = await this.prisma.userSession.create({
          data: {
            userId: user.id,
            fcmToken: fcmToken || null,
          },
        });
      } else if (fcmToken) {
        userSession = await this.prisma.userSession.update({
          where: { id: userSession.id },
          data: { fcmToken },
        });
      }

      return {
        status: true,
        message: 'Authentication successful',
        user,
        userSession,
      };
    } catch (error) {
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async signup(signupDto: SignupDto) {
    try {
      // Check if user already exists with this email
      const existingUser = await this.prisma.user.findFirst({
        where: {
          email: signupDto.email,
        },
      });

      if (existingUser) {
        throw new BadRequestException(userMessage.USER_EMAIL_EXIST);
      }

      // Create user in Firebase
      const firebaseUser = await admin.auth().createUser({
        email: signupDto.email,
        displayName: `${signupDto.firstName} ${signupDto.lastName}`,
        emailVerified: false,
      });

      // Create user in database
      const user = await this.prisma.user.create({
        data: {
          firebaseUid: firebaseUser.uid,
          email: signupDto.email,
          firstName: signupDto.firstName,
          lastName: signupDto.lastName,
          authType: signupDto.authType,
          emailVerified: false,
        },
      });

      // Create user session
      const userSession = await this.prisma.userSession.create({
        data: {
          userId: user.id,
          fcmToken: signupDto.fcmToken || null,
        },
      });

      // Send verification email
      await this.sendVerificationEmail(user.email);

      return {
        status: true,
        message: 'User registered successfully',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(error.message || authMessages.AUTH_ERROR);
    }
  }

  async login(loginDto: LoginDto) {
    try {
      // Find user by email
      const user = await this.prisma.user.findFirst({
        where: {
          email: loginDto.email,
          status: STATUS.ENABLED,
          isDeleted: false,
        },
      });

      if (!user) {
        throw new BadRequestException(userMessage.USER_NOT_FOUND);
      }

      // Verify auth type matches
      if (user.authType !== loginDto.authType) {
        if (loginDto.authType === AUTH_TYPE.EMAIL) {
          throw new BadRequestException(authMessages.SOCIAL);
        } else {
          throw new BadRequestException(authMessages.EMAIL);
        }
      }

      // Create or update user session
      let userSession = await this.prisma.userSession.findFirst({
        where: { userId: user.id, status: STATUS.ENABLED },
      });

      if (!userSession) {
        userSession = await this.prisma.userSession.create({
          data: {
            userId: user.id,
            fcmToken: loginDto.fcmToken || null,
          },
        });
      } else if (loginDto.fcmToken) {
        userSession = await this.prisma.userSession.update({
          where: { id: userSession.id },
          data: { fcmToken: loginDto.fcmToken },
        });
      }

      return {
        status: true,
        message: 'Login successful',
        user,
        userSession,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async sendVerificationEmail(email: string) {
    // Find user by email
    const user = await this.prisma.user.findFirst({
      where: { email, status: STATUS.ENABLED, isDeleted: false },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (user.emailVerified) {
      throw new BadRequestException(authMessages.EMAIL_ALREADY_VERIFIED);
    }

    // Check if there's a recent OTP request (prevent spam)
    const recentOtp = await this.prisma.otpVerification.findFirst({
      where: {
        email: user.email,
        status: STATUS.ENABLED,
        createdAt: {
          gte: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        },
      },
    });

    if (recentOtp) {
      throw new BadRequestException(authMessages.OTP_REQUEST_TOO_FREQUENT);
    }

    // Disable any existing OTPs for this email
    await this.prisma.otpVerification.updateMany({
      where: {
        email: user.email,
        status: STATUS.ENABLED,
      },
      data: {
        status: STATUS.DISABLED,
      },
    });

    // Generate new OTP
    const otp = await generateRandomId();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Save OTP to database
    await this.prisma.otpVerification.create({
      data: {
        email: user.email,
        otp,
        expiresAt,
      },
    });

    // Send OTP email using Zepto Mail
    const emailSent = await this.emailService.sendOtpEmail(
      user.email,
      otp,
      user.firstName ?? '',
    );

    if (!emailSent) {
      // If email sending fails, disable the OTP
      await this.prisma.otpVerification.updateMany({
        where: {
          email: user.email,
          otp,
          status: STATUS.ENABLED,
        },
        data: {
          status: STATUS.DISABLED,
        },
      });

      throw new BadRequestException(authMessages.EMAIL_SEND_FAILED);
    }

    return {
      status: true,
      message: 'Verification email sent successfully',
    };
  }

  async verifyEmail(email: string, verifyEmailDto: VerifyEmailDto) {
    // Find user by email
    const user = await this.prisma.user.findFirst({
      where: { email, status: STATUS.ENABLED, isDeleted: false },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    // Check if email is already verified
    if (user.emailVerified) {
      throw new BadRequestException(authMessages.EMAIL_ALREADY_VERIFIED);
    }

    // Find valid OTP record
    const otpRecord = await this.prisma.otpVerification.findFirst({
      where: {
        email: user.email,
        otp: verifyEmailDto.otp,
        status: STATUS.ENABLED,
        expiresAt: { gt: new Date() },
      },
    });

    if (!otpRecord) {
      throw new BadRequestException(authMessages.ENTER_VALID_OTP);
    }

    // Update user email verification status
    await this.prisma.user.update({
      where: { id: user.id },
      data: { emailVerified: true },
    });

    // Update Firebase user
    try {
      await admin.auth().updateUser(user.firebaseUid, {
        emailVerified: true,
      });
    } catch (error) {
      console.error('Error updating Firebase user:', error);
      // Continue with the process even if Firebase update fails
    }

    // Disable OTP
    await this.prisma.otpVerification.update({
      where: { id: otpRecord.id },
      data: { status: STATUS.DISABLED },
    });

    return {
      status: true,
      message: 'Email verified successfully',
      user: {
        id: user.id,
        email: user.email,
        emailVerified: true,
      },
    };
  }

  async updateFcmToken(firebaseUid: string, fcmToken: string) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    await this.prisma.userSession.updateMany({
      where: { userId: user.id, status: STATUS.ENABLED },
      data: { fcmToken },
    });

    return {
      status: true,
      message: 'FCM token updated successfully',
    };
  }

  async logout(firebaseUid: string, sessionId?: string) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (sessionId) {
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    } else {
      await this.prisma.userSession.updateMany({
        where: { userId: user.id, status: STATUS.ENABLED },
        data: { status: STATUS.DISABLED, isDeleted: true },
      });
    }

    return {
      status: true,
      message: 'Logged out successfully',
    };
  }
}
