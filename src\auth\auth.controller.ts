import {
  Controller,
  Post,
  Patch,
  Delete,
  Body,
  Headers,
  Query,
  UseGuards,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { FirebaseAuthGuard } from './guards/firebase-auth.guard';
import { AuthDto, VerifyEmailDto, SignupDto, LoginDto, SendVerificationEmailDto } from './dto/auth.dto';
import { AUTH_TYPE } from '@prisma/client';
import { ApiTags, ApiBearerAuth, ApiHeader, ApiOperation } from '@nestjs/swagger';
import * as admin from 'firebase-admin';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @ApiOperation({ summary: 'User Registration/Sign-Up' })
  async signup(@Body() signupDto: SignupDto) {
    return this.authService.signup(signupDto);
  }

  @Post('login')
  @ApiOperation({ summary: 'User Login' })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  // @Post()
  // @UseGuards(FirebaseAuthGuard)
  // @ApiOperation({ summary: 'Authenticate user Login/Sign-Up (Legacy)' })
  // @ApiBearerAuth()
  // @ApiHeader({ name: 'authType', enum: AUTH_TYPE })
  // @ApiHeader({ name: 'notificationToken', required: false })
  // async authenticate(
  //   @Req() req: any,
  //   @Headers('authType') authType: AUTH_TYPE,
  //   @Headers('notificationToken') fcmToken?: string,
  //   @Body() authDto?: AuthDto,
  // ) {
  //   return this.authService.authenticate(
  //     req.user.uid,
  //     authType,
  //     fcmToken,
  //     authDto,
  //   );
  // }

  // @Post('send-verification-email')
  // @ApiOperation({ summary: 'Send verification email (Legacy)' })
  // @UseGuards(FirebaseAuthGuard)
  // @ApiBearerAuth()
  // async sendVerificationEmailLegacy(@Req() req: any) {
  //   // Get user from Firebase
  //   const firebaseUser = await admin.auth().getUser(req.user.uid);
  //   if (!firebaseUser.email) {
  //     throw new BadRequestException('User email not found');
  //   }
  //   return this.authService.sendVerificationEmail(firebaseUser.email);
  // }

  @Post('send-verification-email')
  @ApiOperation({ summary: 'Send verification email' })
  async sendVerificationEmail(@Body() dto: SendVerificationEmailDto) {
    return this.authService.sendVerificationEmail(dto.email);
  }

  @Patch('verify-email')
  @ApiOperation({ summary: 'Verify email with OTP (Legacy)' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async verifyEmailLegacy(@Req() req: any, @Body() verifyEmailDto: VerifyEmailDto) {
    // Get user from Firebase
    const firebaseUser = await admin.auth().getUser(req.user.uid);
    if (!firebaseUser.email) {
      throw new BadRequestException('User email not found');
    }
    return this.authService.verifyEmail(firebaseUser.email, verifyEmailDto);
  }

  @Patch('verify-email/v2')
  @ApiOperation({ summary: 'Verify email with OTP' })
  async verifyEmail(
    @Body('email') email: string,
    @Body('otp') otp: string,
  ) {
    const verifyEmailDto: VerifyEmailDto = { otp };
    return this.authService.verifyEmail(email, verifyEmailDto);
  }

  @Patch('fcm-token')
  @ApiOperation({ summary: 'FCM Token Update' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notificationToken' })
  async updateFcmToken(
    @Req() req: any,
    @Headers('notificationToken') fcmToken: string,
  ) {
    return this.authService.updateFcmToken(req.user.uid, fcmToken);
  }

  @Delete('logout')
  @ApiOperation({ summary: 'Logout user' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notificationToken', required: false })
  async logout(@Req() req: any, @Query('sessionId') sessionId?: string) {
    return this.authService.logout(req.user.uid, sessionId);
  }
}
